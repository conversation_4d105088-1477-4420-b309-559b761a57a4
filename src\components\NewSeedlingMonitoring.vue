<template>
    <div class="new-seedling-monitoring" v-if="dialogVisible">

        <!-- 查询无结果提示框 -->
        <div class="no-video-dialog-wrapper">
            <el-dialog
                title="提示"
                :visible.sync="noVideoDialog.visible"
                width="374px"
                :show-close="false"
                :close-on-click-modal="true"
                :close-on-press-escape="false"
                :modal-append-to-body="false"
                center
                @close="closeNoVideoDialog"
            >
                <img src="../assets/image/centralControlPlatform/dialogLine.png" alt="" class="dialog-line">
                <div class="dialog-content">
                    <div class="no-video-icon">
                        <img src="../assets/image/centralControlPlatform/noVideo.png" alt="无视频" />
                    </div>
                    <p class="no-video-text">无视频</p>
                </div>
            </el-dialog>
        </div>

        <!-- 苗情检测弹窗 -->
        <div class="seedling-detection-dialog">
            <el-dialog
                title="视频回放"
                :visible.sync="dialogVisible"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :modal="false"
                :modal-append-to-body="false"
                width="1256px"
                custom-class="seedling-detection-dialog"
            >
                <!-- 关闭按钮 -->
                <img src="../assets/image/eppoWisdom/close.png" alt="关闭" class="clone" @click="closeDialog" />
                <div class="wire"></div>

                <!-- 弹窗内容容器 -->
                <div class="content-container">
                    <!-- 第一行控制区域 -->
                    <div class="control-row">
                        <div class="control-left">
                            <el-select
                                v-model="selectedArea"
                                class="area-select formStyle"
                                placeholder="请选择区域"
                                popper-class="selectStyle_list"
                                @change="onAreaChange"
                            >
                                <el-option
                                    v-for="area in areaOptions"
                                    :key="area.value"
                                    :label="area.label"
                                    :value="area.value"
                                />
                            </el-select>
                            <el-select
                                v-model="selectedDevice"
                                class="device-select formStyle"
                                placeholder="请选择设备"
                                popper-class="selectStyle_list"
                                @change="onDeviceChange"
                            >
                                <el-option
                                    v-for="device in deviceOptions"
                                    :key="device.value"
                                    :label="device.label"
                                    :value="device.value"
                                />
                            </el-select>
                            <el-select
                                v-model="selectedDate"
                                class="date-select formStyle"
                                placeholder="选择日期"
                                popper-class="selectStyle_list"
                                @change="onDateChange"
                            >
                                <el-option
                                    v-for="date in dateOptions"
                                    :key="date.value"
                                    :label="date.label"
                                    :value="date.value"
                                />
                            </el-select>
                            <div class="time-range-container formStyle">
                                <el-time-picker
                                    v-model="timeRange"
                                    class="time-range-picker formStyle"
                                    is-range
                                    range-separator="至"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    format="HH:mm:ss"
                                    value-format="HH:mm:ss"
                                    @change="onTimeRangeChange"
                                />
                            </div>
                            <button class="query-btn" @click="queryData">查询</button>
                        </div>
                        <div class="control-right">
                            <button class="realtime-btn" @click="goToRealtime">实时</button>
                        </div>
                    </div>

                    <!-- 视频展示区域 -->
                    <div class="video-container">
                        <div class="video-border">
                            <div class="video-window">
                                <!-- 视频内容区域 -->
                                <div class="video-content" v-if="videoSrc">
                                    <video
                                        ref="videoElement"
                                        class="video-element"
                                        :src="videoSrc"
                                        preload="metadata"
                                        @error="handleVideoError"
                                        @timeupdate="updateProgress"
                                        @loadedmetadata="onVideoLoaded"
                                        @ended="onVideoEnded"
                                        @play="onVideoPlay"
                                        @pause="onVideoPause"
                                    >
                                        您的浏览器不支持视频播放
                                    </video>
                                    <!-- 视频内部信息显示 -->
                                    <div class="video-info-overlay">
                                        <div class="device-datetime-info">
                                            <div class="device-info">{{ currentDeviceId }}</div>
                                            <div class="datetime-info">{{ currentDateTime }} {{ currentWeekday }}</div>
                                        </div>
                                    </div>
                                    <!-- 视频进度条 -->
                                    <div class="video-progress-container">
                                        <div class="video-progress-bar" @click="seekVideo">
                                            <div class="progress-track">
                                                <div class="progress-fill" :style="{ width: progressPercent + '%' }"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 视频控制按钮 -->
                                    <div class="video-controls">
                                        <div class="control-left">
                                            <button class="control-btn play-pause-btn" @click="togglePlayPause">
                                                <img :src="isPlaying ? videostopIcon : videoplayIcon" alt="" />
                                            </button>
                                        </div>
                                        <div class="control-right">
                                            <button class="control-btn fullscreen-btn" @click="captureMainVideo">
                                                <img :src="camera" alt="" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="video-placeholder" v-else>
                                    <img src="../assets/image/monitoringCenter/videoPlay.png" alt="视频播放" class="video-placeholder-icon" />
                                    <div class="video-info">{{ getVideoDisplayText() }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
export default {
    name: 'NewSeedlingMonitoring',
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            // 大屏展示数据
            hasVideoData: false, // 控制是否有视频数据，暂时设为false
            videoSrc: '', // 视频源地址，待接口接入时使用
            currentTime: '2022-05-26 13:26:31', // 当前时间，参考图片中的时间格式

            // 弹窗数据
            dialogVisible: false,
            selectedArea: '',
            selectedDevice: '',
            selectedDate: '',
            timeRange: [], // 时间范围选择器的值 [开始时间, 结束时间]
            startTime: '',
            endTime: '',
            isPlaying: false,
            isMainVideoPlaying: false, // 主视频播放状态
            progressPercent: 0, // 视频播放进度百分比
            videoDuration: 0, // 视频总时长
            progressUpdatePending: false, // 进度更新标志
            currentDeviceId: '',
            currentDateTime: '',
            currentWeekday: '',
            
            // 图标
            videoplayIcon: require('../assets/image/monitoringCenter/videoPlay.png'),
            videostopIcon: require('../assets/image/monitoringCenter/videoStop.png'),
            allScreenIcon: require('../assets/image/monitoringCenter/allScreen.png'),
            camera: require('../assets/image/centralControlPlatform/camera.png'),
            
            // 区域选项
            areaOptions: [
                { value: 'qinghai', label: '青海地区' },
                { value: 'area1', label: '玉米种植区域1' },
                { value: 'area2', label: '玉米种植区域2' },
                { value: 'area3', label: '玉米种植区域3' }
            ],

            // 设备选项
            deviceOptions: [
                { value: 'device1', label: '苗情监控设备1' },
                { value: 'device2', label: '苗情监控设备2' },
                { value: 'device3', label: '苗情监控设备3' }
            ],

            // 日期选项
            dateOptions: [
                { value: '2025-07-08', label: '2025-07-08' },
                { value: '2025-07-07', label: '2025-07-07' },
                { value: '2025-07-06', label: '2025-07-06' },
                { value: '2025-07-05', label: '2025-07-05' },
                { value: '2025-07-04', label: '2025-07-04' }
            ],

            // 无视频提示框
            noVideoDialog: {
                visible: false
            }

        }
    },
    watch: {
        visible(newVal) {
            this.dialogVisible = newVal
            if (newVal) {
                this.initializeData()
            }
        },
        dialogVisible(newVal) {
            this.$emit('update:visible', newVal)
        }
    },
    mounted() {
        // 初始化苗情视频数据
        this.loadSeedlingVideoData()

        // 初始化时间显示
        this.updateCurrentTime()
        // 定时更新时间（如果需要实时时间）
        this.timeInterval = setInterval(() => {
            this.updateCurrentTime()
        }, 1000)
    },
    beforeDestroy() {
        // 清理定时器
        if (this.timeInterval) {
            clearInterval(this.timeInterval)
        }
    },
    methods: {
        // 加载苗情视频数据
        loadSeedlingVideoData() {
            // TODO: 调用苗情视频接口
            // SeedlingService.getSeedlingVideoData()
            // .then(res => {
            //     if (res && res.videoUrl) {
            //         this.videoSrc = res.videoUrl;
            //         this.hasVideoData = true;
            //         this.currentTime = res.captureTime || this.getCurrentTimeString();
            //     } else {
            //         this.hasVideoData = false;
            //     }
            // })
            // .catch(err => {
            //     console.error('获取苗情视频数据失败:', err);
            //     this.hasVideoData = false;
            // });

            // 暂时模拟有视频数据用于测试按钮效果
            this.hasVideoData = true
            this.videoSrc = 'https://www.w3schools.com/html/mov_bbb.mp4' // 测试视频
            this.currentTime = this.getCurrentTimeString()
        },

        // 打开弹窗
        openDialog() {
            this.dialogVisible = true
            this.initializeData()
        },
        
        // 关闭弹窗
        closeDialog() {
            this.dialogVisible = false
        },
        
        // 初始化数据
        initializeData() {
            // 设置默认值
            if (this.areaOptions.length > 0) {
                this.selectedArea = this.areaOptions[0].value
            }
            if (this.deviceOptions.length > 0) {
                this.selectedDevice = this.deviceOptions[0].value
            }
            if (this.dateOptions.length > 0) {
                this.selectedDate = this.dateOptions[0].value
            }
            this.timeRange = ['00:00:00', '23:59:59'] // 设置默认时间范围
            this.startTime = '00:00:00'
            this.endTime = '23:59:59'
            this.updateDateTime()
        },
        
        // 跳转到实时监控
        goToRealtime() {
            console.log('跳转到实时监控')
            this.dialogVisible = false
            this.$emit('go-to-realtime')
        },
        
        // 区域选择改变
        onAreaChange() {
            console.log('区域选择改变:', this.selectedArea)
            this.loadVideoData()
        },
        
        // 设备选择改变
        onDeviceChange() {
            console.log('设备选择改变:', this.selectedDevice)
            this.loadVideoData()
        },
        
        // 日期选择改变
        onDateChange() {
            console.log('日期选择改变:', this.selectedDate)
            this.loadVideoData()
        },
        
        // 时间范围选择改变
        onTimeRangeChange(value) {
            console.log('时间范围选择改变:', value)
            
            // 更新开始时间和结束时间
            if (value && Array.isArray(value) && value.length === 2) {
                this.startTime = value[0]
                this.endTime = value[1]
            } else {
                this.startTime = ''
                this.endTime = ''
            }
            
            this.loadVideoData()
        },
        
        // 查询数据
        queryData() {
            console.log('查询数据:', {
                area: this.selectedArea,
                device: this.selectedDevice,
                date: this.selectedDate,
                startTime: this.startTime,
                endTime: this.endTime
            })
            this.loadVideoData(true) // 传入参数表示是用户主动查询
        },

        // 加载视频数据
        loadVideoData(isUserQuery = false) {
            // TODO: 根据选择的参数加载对应的视频数据
            // 暂时模拟有视频数据用于测试
            this.videoSrc = 'https://www.w3schools.com/html/mov_bbb.mp4'
            this.updateDateTime()

            // 如果是用户主动查询且没有查到视频数据，显示提示框
            if (isUserQuery && !this.videoSrc) {
                this.showNoVideoDialog()
            }
        },

        // 显示无视频提示框
        showNoVideoDialog() {
            this.noVideoDialog.visible = true
        },

        // 关闭无视频提示框
        closeNoVideoDialog() {
            this.noVideoDialog.visible = false
        },
        
        // 获取当前日期
        getCurrentDate() {
            const now = new Date()
            return now.getFullYear() + '-' +
                   String(now.getMonth() + 1).padStart(2, '0') + '-' +
                   String(now.getDate()).padStart(2, '0')
        },

        // 获取当前时间字符串
        getCurrentTimeString() {
            const now = new Date()
            return now.getFullYear() + '-' +
                   String(now.getMonth() + 1).padStart(2, '0') + '-' +
                   String(now.getDate()).padStart(2, '0') + ' ' +
                   String(now.getHours()).padStart(2, '0') + ':' +
                   String(now.getMinutes()).padStart(2, '0') + ':' +
                   String(now.getSeconds()).padStart(2, '0')
        },
        
        // 更新时间显示
        updateDateTime() {
            const now = new Date()
            const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
            
            this.currentDateTime = now.getFullYear() + '-' + 
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0')
            
            this.currentWeekday = weekdays[now.getDay()]
            this.currentDeviceId = this.selectedDevice ? this.selectedDevice.toUpperCase() : 'DEVICE1'
        },
        
        // 获取视频显示文本
        getVideoDisplayText() {
            if (this.selectedArea && this.selectedDevice && this.selectedDate) {
                const timeInfo = `${this.startTime || '00:00:00'} - ${this.endTime || '23:59:59'}`
                return `${this.selectedArea} - ${this.selectedDevice} - ${this.selectedDate} - ${timeInfo}`
            }
            return '请选择查询条件'
        },
        
        // 播放/暂停切换
        togglePlayPause() {
            if (this.$refs.videoElement) {
                if (this.isPlaying) {
                    this.$refs.videoElement.pause()
                } else {
                    this.$refs.videoElement.play()
                }
                // 不在这里直接修改状态，让视频事件来处理
                // this.isPlaying = !this.isPlaying
            }
        },
        
        // 全屏切换
        toggleFullscreen() {
            console.log('截图')
        },

        // 视频加载完成
        onVideoLoaded() {
            if (this.$refs.videoElement) {
                this.videoDuration = this.$refs.videoElement.duration
            }
        },

        // 更新播放进度
        updateProgress() {
            if (this.$refs.videoElement && this.videoDuration > 0) {
                const currentTime = this.$refs.videoElement.currentTime
                const newProgressPercent = Math.min(100, (currentTime / this.videoDuration) * 100)

                // 使用 requestAnimationFrame 来优化更新频率
                if (!this.progressUpdatePending) {
                    this.progressUpdatePending = true
                    requestAnimationFrame(() => {
                        this.progressPercent = newProgressPercent
                        this.progressUpdatePending = false
                    })
                }
            }
        },

        // 视频播放事件
        onVideoPlay() {
            this.isPlaying = true
        },

        // 视频暂停事件
        onVideoPause() {
            this.isPlaying = false
        },

        // 视频播放完成事件
        onVideoEnded() {
            this.isPlaying = false
            this.progressPercent = 100
        },

        // 主视频播放事件
        onMainVideoPlay() {
            this.isMainVideoPlaying = true
        },

        // 主视频暂停事件
        onMainVideoPause() {
            this.isMainVideoPlaying = false
        },

        // 主视频播放完成事件
        onMainVideoEnded() {
            this.isMainVideoPlaying = false
        },

        // 切换主视频播放/暂停
        toggleMainVideoPlay() {
            const videoElement = this.$refs.mainVideoElement
            if (videoElement) {
                if (this.isMainVideoPlaying) {
                    videoElement.pause()
                } else {
                    videoElement.play()
                }
            }
        },

        // 主视频截图
        captureMainVideo() {
            console.log('主视频截图')
            // TODO: 实现截图功能
            // 可以使用 canvas 来截取视频当前帧
        },

        // 点击进度条跳转
        seekVideo(event) {
            if (this.$refs.videoElement && this.videoDuration > 0) {
                const progressBar = event.currentTarget
                const rect = progressBar.getBoundingClientRect()
                const clickX = event.clientX - rect.left
                const progressWidth = rect.width
                const clickPercent = Math.max(0, Math.min(1, clickX / progressWidth))
                const seekTime = clickPercent * this.videoDuration

                this.$refs.videoElement.currentTime = seekTime

                // 立即更新进度条显示
                this.progressPercent = clickPercent * 100

                // 如果视频已经结束，跳转后重置播放状态
                if (this.$refs.videoElement.ended) {
                    this.isPlaying = false
                }
            }
        },
        
        // 视频错误处理
        handleVideoError() {
            console.error('苗情视频加载失败')
            this.hasVideoData = false
            this.videoSrc = ''
        },

        // 更新当前时间显示
        updateCurrentTime() {
            this.currentTime = this.getCurrentTimeString()
        }
    }
}
</script>

<style lang="less" scoped>
// 苗情监测显示区域样式（与原组件一致）
.new-seedling-monitoring {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .seedling-video-container {
        width: 356px;
        height: 200px;
        position: relative;
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 4px;
        overflow: hidden;
        background: #000;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            border-color: rgba(0, 255, 255, 0.4);
            box-shadow: 0 0 8px rgba(0, 255, 255, 0.2);
        }

        .seedling-video-player {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .seedling-video-info {
            position: absolute;
            top: 8px;
            left: 8px;
            // background: rgba(0, 0, 0, 0.6);
            padding: 4px 8px;
            border-radius: 2px;

            .seedling-video-time {
                color: rgba(255, 255, 255, 0.9);
                font-size: 12px;
                font-weight: normal;
            }
        }

        // 主视频控制按钮
        .main-video-controls {
            position: absolute;
            bottom: 8px;
            right: 8px;
            display: flex;
            align-items: center;
            gap: 115px;

            .control-left,
            .control-right {
                display: flex;
                align-items: center;
            }

            .main-control-btn {
                width: 32px;
                height: 32px;
                background: transparent;
                border: none;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    // background: rgba(0, 0, 0, 0.8);
                    transform: scale(1.05);
                }

                img {
                    width: 20px;
                    height: 20px;
                    filter: brightness(0) invert(1); // 将图标变为白色
                }
            }

            .play-btn {
                img {
                    width: 16px;
                    height: 16px;
                }
            }

            .dialog-btn {
                img {
                    width: 18px;
                    height: 18px;
                }
            }
        }
    }

    .seedling-no-data-container {
        width: 356px;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            border-color: rgba(0, 255, 255, 0.4);
            box-shadow: 0 0 8px rgba(0, 255, 255, 0.2);
        }

        .seedling-no-data-content {
            text-align: center;

            img {
                width: 60px;
                height: 60px;
                margin-bottom: 12px;
                opacity: 0.6;
            }

            .seedling-no-data-text {
                color: rgba(255, 255, 255, 0.6);
                font-size: 16px;
                font-weight: 400;
            }
        }
    }
}

// 苗情检测弹窗样式
.seedling-detection-dialog ::v-deep .el-dialog {
    height: 854px!important;
    margin-top: calc(50vh - 400px) !important;

    .el-dialog__header {
        padding: 23px 30px 23px 30px;
        text-align: left;

        .el-dialog__title {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #DFEEF3;
            position: relative;
        }
    }

    .el-dialog__body {
        height: calc(100% - 114px);
        padding: 0 52px 30px 52px!important;
        position: relative;

        .clone {
            position: absolute;
            top: -55px!important;
            right: 24px!important;
            cursor: pointer;
            z-index: 10;
        }
        .wire{
            height: 2px;
            position: relative;
            top: -15px;
            background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
            margin-bottom: 20px;
        }

        .content-container {
            height: 100%;

            // 控制行样式
            .control-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24px;
                height: 32px;

                .control-left {
                    display: flex;
                    align-items: center;
                    gap: 20px;

                    .query-btn {
                        width: 80px;
                        height: 32px;
                        background: rgba(0,245,255,0);
                        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5);
                        border-radius: 2px;
                        border: 0;
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #DFEEF3;
                        cursor: pointer;

                        &:hover {
                            background: rgba(0,245,255,0.1);
                        }
                    }

                    .area-select, .device-select, .date-select {
                        width: 140px;
                        height: 32px;
                    }

                    .time-range-container {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        height: 32px;
                        width: 240px;
                        .time-range-picker {
                            width: 280px;
                            height: 32px;
                        }
                    }
                }

                .control-right {
                    .realtime-btn {
                        width: 80px;
                        height: 32px;
                        background: rgba(0,245,255,0);
                        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5);
                        border-radius: 2px;
                        border: 0;
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #DFEEF3;
                        cursor: pointer;

                        &:hover {
                            background: rgba(0,245,255,0.1);
                        }
                    }
                }
            }

            // 视频容器样式
            .video-container {
                width: 100%;
                display: flex;
                justify-content: center;

                .video-border {
                    width: 1152px;
                    height: 662px;
                    border: 1px solid rgba(0, 244, 253, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: rgba(0, 0, 0, 0.1);

                    .video-window {
                        width: 1120px;
                        height: 630px;
                        background: #000000;
                        border-radius: 2px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        position: relative;

                        .video-content {
                            width: 100%;
                            height: 100%;
                            position: relative;

                            .video-element {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }

                            .video-info-overlay {
                                position: absolute;
                                top: 10px;
                                left: 10px;
                                color: #DFEEF3;
                                font-size: 14px;
                                font-family: Microsoft YaHei;
                                z-index: 2;

                                .device-datetime-info {
                                    .device-info {
                                        margin-bottom: 5px;
                                    }
                                    .datetime-info {
                                        opacity: 0.8;
                                    }
                                }
                            }

                            // 视频进度条样式
                            .video-progress-container {
                                position: absolute;
                                bottom: 50px;
                                left: 10px;
                                right: 10px;
                                z-index: 3;

                                .video-progress-bar {
                                    width: 100%;
                                    height: 6px;
                                    cursor: pointer;

                                    .progress-track {
                                        width: 100%;
                                        height: 100%;
                                        background: rgba(255, 255, 255, 0.3);
                                        border-radius: 3px;
                                        overflow: hidden;

                                        .progress-fill {
                                            height: 100%;
                                            background: #00E4FF;
                                            border-radius: 3px;
                                            transition: width 0.3s linear;
                                            will-change: width;
                                        }
                                    }

                                    &:hover .progress-track {
                                        background: rgba(255, 255, 255, 0.4);
                                    }
                                }
                            }

                            .video-controls {
                                position: absolute;
                                bottom: 10px;
                                left: 519px;
                                right: 10px;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                z-index: 4; // 提高层级

                                .control-btn {
                                    width: 32px;
                                    height: 32px;
                                    background: transparent;
                                    border: none;
                                    border-radius: 4px;
                                    cursor: pointer;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;

                                    img {
                                        width: 24px;
                                        height: 24px;
                                    }

                                    // &:hover {
                                    //     background: rgba(0, 0, 0, 0.7);
                                    // }
                                }
                            }
                        }

                        .video-placeholder {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;

                            .video-placeholder-icon {
                                width: 64px;
                                height: 64px;
                                opacity: 0.5;
                                margin-bottom: 20px;
                            }

                            .video-info {
                                font-size: 16px;
                                font-family: Microsoft YaHei;
                                color: #DFEEF3;
                                opacity: 0.7;
                            }
                        }
                    }
                }
            }
        }
    }
}

// 自定义下拉选择框和日期选择器样式
.seedling-detection-dialog ::v-deep .formStyle {
    .el-input__inner {
        color: rgba(254, 255, 255, 0.6) !important;
        background: rgba(0,245,255,0) !important;
        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5) !important;
        border-radius: 2px !important;
        border: 0 !important;
    }

    .el-select .el-input .el-select__caret {
        color: rgba(254, 255, 255, 0.6) !important;
    }

    .el-date-editor .el-input__inner {
        color: rgba(254, 255, 255, 0.6) !important;
        background: rgba(0,245,255,0) !important;
        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5) !important;
        border-radius: 2px !important;
        border: 0 !important;
    }

    .el-date-editor .el-input__prefix {
        color: rgba(254, 255, 255, 0.6) !important;
    }
    .el-date-editor .el-range__close-icon {
       line-height: 27px !important;
    }

    .el-time-picker .el-input__inner {
        color: rgba(254, 255, 255, 0.6) !important;
        background: rgba(0,245,255,0) !important;
        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5) !important;
        border-radius: 2px !important;
        border: 0 !important;
    }

    .el-time-picker .el-input__icon {
        line-height: 27px !important;
        color: rgba(254, 255, 255, 0.6) !important;
    }

    .el-time-picker .el-range-separator {
        color: rgba(254, 255, 255, 0.6) !important;
        line-height: 27px !important;
    }

}

// 无视频提示框样式
.no-video-dialog-wrapper {
    :deep(.v-modal) {
        background: rgba(0, 0, 0, 0.5) !important;
    }

    :deep(.el-dialog) {
        width: 374px !important;
        height: 246px;
        background: #003D42;
        font-family: MicrosoftYaHei;
        margin-top: calc(50vh - 118px) !important;

        .el-dialog__header {
            height: 60px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            .el-dialog__title {
                color: #DFEEF3;
                font-size: 16px;
                font-weight: bold;
            }
        }

        .el-dialog__body {
            padding: 0;
            height: calc(100% - 60px);
            display: flex;
            flex-direction: column;
            color: #DFEEF3;

            .dialog-line {
                position: relative;
                top: -20px;
            }

            .dialog-content {
                // flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 15px;

                .no-video-icon {
                    margin-bottom: 20px;

                    img {
                        width: 64px;
                        height: 64px;
                        opacity: 0.8;
                    }
                }

                .no-video-text {
                    color: #DFEEF3;
                    font-size: 20px;
                    margin: 0;
                    text-align: center;
                    line-height: 1.5;
                }
            }
        }
    }
}
</style>
